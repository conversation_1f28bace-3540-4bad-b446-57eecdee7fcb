const express=require('express')
const jwt=require('jsonwebtoken')
const cookieParser=require('cookie-parser')
const bcrypt=require('bcrypt')
const path=require('path')
const UserModel=require('./models/user')
const app=express()

app.set("view engine",'ejs')
app.use(express.json())
app.use(express.urlencoded({extended:true}))
app.use(express.static(path.join(__dirname,'public')))
app.use(cookieParser())

app.get('/',(req,res)=>{
    res.render('index')
})

app.post('/register',(req,res)=>{
    const {name,username,email,password,age}=req.body
    if(user) return res.status(400).json("user is already exists")
    
    bcrypt.genSalt(10,(err,salt)=>{
        bcrypt.hash(password,salt,async(err,hash)=>{
            await userModel.create({
                username,
                name,
                age,
                email,
                password:hash
            })
            jwt.sign({email:email,userid:user._id},"shoess")
            res.cookie('token',token)
            res.redirect('/login')
        })
    })
})
app.listen(5000,()=>{
    console.log("server is running on 5000")
})