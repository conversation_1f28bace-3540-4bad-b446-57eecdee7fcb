const mongoose=require('mongoose')
mongoose.connect('mongodb://0.0.0.0:27017/postCreationApp').then(()=>{
    console.log("database connected successfully");
})

const userSchema=new mongoose.Schema({
    name:String,
    username:String,
    email:String,
    password:String,
    age:Number,
    post:[{
        type:mongoose.Schema.Types.ObjectId,
        ref:'post'
    }]
})

module.exports=mongoose.model('user',userSchema)