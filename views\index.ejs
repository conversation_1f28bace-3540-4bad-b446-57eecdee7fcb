<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <!-- <link rel="stylesheet" href="stylesheets/style.css"> -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

</head>
<body>
    <div class="w-full h-screen bg-zinc-800">
        <div class="w-full h-full flex justify-center items-center">
            <div class="w-[480px] h-[480px] bg-transparent text-white border-1 border-blue-800 rounded-xl">
               <h1 class="text-xl font-semibold py-5 text-center">Create Account </h1>
               <form  method="post" action="/register">
                <div class="flex mb-4 justify-center items-center gap-3">
                    <label class="text-xl" for="name">Name :</label>
                    <input type="text" 
                    class="w-70 rounded-md p-2  bg-gray-500 outline-none border-1"
                    name="name" id="name" placeholder="Enter the name">
                </div>
                

                <div class="flex justify-center mb-4 items-center gap-3">
                    <label class="text-xl" for="username">Username:</label>
                    <input type="text" 
                    class="w-70 rounded-md p-2 bg-gray-500 outline-none border-1"
                    name="username" id="username" placeholder="Enter the Username">
                </div>
                
                <div class="flex justify-center mb-4 items-center gap-3">
                    <label class="text-xl" for="email">Email :</label>
                    <input type="text" 
                    class="w-70 rounded-md p-2 outline-none bg-gray-500 border-1"
                    name="email" id="email" placeholder="Enter the email">
                </div>
                
                <div class="flex justify-center mb-4 items-center gap-3">
                    <label class="text-xl" for="password">password :</label>
                    <input type="password" 
                    class="w-70 rounded-md p-2 outline-none border-1 bg-gray-500"
                    name="password" id="password" placeholder="Enter the password">
                </div>
                
                <div class="flex justify-center mt-4 items-center gap-3">
                    <label class="text-xl" for="name">Age :</label>
                    <input type="number" 
                    class="w-70 rounded-md p-2 outline-none border-1 bg-gray-500"
                    name="age" id="age" placeholder="Enter the age">
                </div>
                
                <input class="text-xl border-1 bg-blue-800 px-3 py-3 w-70 mt-7 mx-25 rounded-md" type="submit" value="Register">
               </form>
            </div>
        </div>
    </div>
</body>
</html>